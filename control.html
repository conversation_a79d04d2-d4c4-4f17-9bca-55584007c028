<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mouse Tracker & Screen Recorder</title>
    <link rel="stylesheet" href="control.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Mouse Tracker & Screen Recorder</h1>
            <div class="status-indicator">
                <span id="recording-status">Ready</span>
                <div id="status-dot" class="status-dot"></div>
            </div>
            <div class="debug-info" id="debugInfo" style="display: none; font-size: 12px; color: #666; margin-top: 5px;">
                Debug: <span id="debugText">No data</span>
            </div>
        </header>

        <div class="main-content">
            <!-- Recording Type Selection -->
            <section class="recording-options">
                <h2>Recording Options</h2>
                <div class="option-group">
                    <label class="option-card">
                        <input type="radio" name="recordingType" value="tab" checked>
                        <div class="card-content">
                            <div class="card-icon">🗂️</div>
                            <div class="card-title">Current Tab</div>
                            <div class="card-description">Record the active browser tab</div>
                        </div>
                    </label>
                    
                    <label class="option-card">
                        <input type="radio" name="recordingType" value="window">
                        <div class="card-content">
                            <div class="card-icon">🪟</div>
                            <div class="card-title">Window</div>
                            <div class="card-description">Record a specific window</div>
                        </div>
                    </label>
                    
                    <label class="option-card">
                        <input type="radio" name="recordingType" value="screen">
                        <div class="card-content">
                            <div class="card-icon">🖥️</div>
                            <div class="card-title">Entire Screen</div>
                            <div class="card-description">Record the full screen</div>
                        </div>
                    </label>
                </div>
            </section>

            <!-- Mouse Tracking Options -->
            <section class="tracking-options">
                <h2>Mouse Tracking</h2>
                <div class="checkbox-group">
                    <label class="checkbox-option">
                        <input type="checkbox" id="enableMouseTracking" checked>
                        <span class="checkmark"></span>
                        <span class="label-text">Enable mouse tracking on selected tab</span>
                    </label>
                    <label class="checkbox-option">
                        <input type="checkbox" id="showMousePath">
                        <span class="checkmark"></span>
                        <span class="label-text">Show mouse path visualization</span>
                    </label>
                </div>
            </section>

            <!-- Target Tab Info -->
            <section class="tab-info" id="tabInfoSection" style="display: none;">
                <h2>Target Tab</h2>
                <div class="tab-details">
                    <p><strong>Tab:</strong> <span id="targetTabTitle">Loading...</span></p>
                    <p><strong>URL:</strong> <span id="targetTabUrl">Loading...</span></p>
                    <p><small><em>Note: Cannot record extension pages or Chrome internal pages. Use regular web pages (http:// or https://).</em></small></p>
                </div>
            </section>

            <!-- Control Buttons -->
            <section class="controls">
                <button id="refreshTarget" class="btn btn-outline">
                    <span class="btn-icon">🔄</span>
                    Refresh Target Tab
                </button>
                <button id="testMouseTracking" class="btn btn-outline">
                    <span class="btn-icon">🖱️</span>
                    Test Mouse Tracking
                </button>
                <button id="openTestPage" class="btn btn-outline">
                    <span class="btn-icon">🧪</span>
                    Open Test Page
                </button>
                <button id="startRecording" class="btn btn-primary">
                    <span class="btn-icon">▶️</span>
                    Start Recording
                </button>
                <button id="stopRecording" class="btn btn-secondary" disabled>
                    <span class="btn-icon">⏹️</span>
                    Stop Recording
                </button>
                <button id="downloadRecording" class="btn btn-success" disabled>
                    <span class="btn-icon">💾</span>
                    Download Recording
                </button>
            </section>

            <!-- Results Section -->
            <section class="results" id="resultsSection" style="display: none;">
                <h2>Recording Results</h2>
                <div class="results-content">
                    <div class="mouse-data">
                        <h3>Mouse Tracking Data</h3>
                        <div id="mouseStats">
                            <p>Coordinates tracked: <span id="coordCount">0</span></p>
                            <p>Clicks recorded: <span id="clickCount">0</span></p>
                        </div>
                        <button id="copyMouseData" class="btn btn-outline">Copy Mouse Data</button>
                        <div id="mouseVisualization"></div>
                    </div>
                    <div class="recording-data">
                        <h3>Screen Recording</h3>
                        <video id="recordedVideo" controls style="max-width: 100%; height: auto;"></video>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script src="control.js"></script>
</body>
</html>
